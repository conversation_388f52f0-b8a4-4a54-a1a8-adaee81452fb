import re
import os
from typing import Optional, Tuple


# --- Real NLP guardrails imports ---
try:
    from presidio_analyzer import AnalyzerEngine
except ImportError:
    AnalyzerEngine = None
try:
    from transformers import pipeline
except ImportError:
    pipeline = None
try:
    from sentence_transformers import SentenceTransformer, util
except ImportError:
    SentenceTransformer = None
try:
    import openai
except ImportError:
    openai = None

import os
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")


# --- Load NLP models/APIs once ---
presidio_analyzer = AnalyzerEngine() if AnalyzerEngine else None
toxic_classifier = pipeline("text-classification", model="unitary/toxic-bert") if pipeline else None
semantic_model = SentenceTransformer('all-MiniLM-L6-v2') if SentenceTransformer else None

# --- Tool risk config ---
TOOL_RISKS = {
    'delete_db': 'high',
    'update_user': 'medium',
    'view_profile': 'low',
    # Add more tool risk ratings as needed
}

# Extended list of unsafe or restricted terms
UNSAFE_WORDS = [
    "inappropriate", "classified", "ignore this", "violence", "kill", "hate", "abuse",
    "suicide", "terrorist", "explosive", "drugs", "nude", "sex", "porn", "racist",
    "discriminate", "bomb", "murder", "weapon", "self-harm", "shoot", "bully",
    "harass", "torture", "injury", "death", "explicit", "crime", "exploit", "attack", "illegal",
    "die", "rape", "dark web",
    "damn", "hell", "shit", "fuck", "f***", "bitch", "bastard", "asshole", "dick",
    "slut", "whore", "crap", "piss", "cunt", "retard", "moron", "stupid", "idiot",
    "dumb", "loser", "fat", "ugly", "jerk", "suck", "screw you", "shut up"
]

# --- Guardrail Helper Functions ---

def safety_classifier(user_input: str) -> bool:
    """
    Detects prompt injection/jailbreak attempts using patterns/keywords.
    """
    patterns = [
        r"ignore all previous instructions",
        r"disregard previous",
        r"pretend to be",
        r"you are now",
        r"repeat after me",
        r"as an ai language model, you can",
        r"bypass",
        r"jailbreak",
        r"unfiltered",
        r"simulate",
        r"write a script to",
        r"system prompt",
        r"my instructions are:",
        r"hack",
        r"exploit",
        r"attack",
        r"breach",
        r"malware",
        r"phishing",
        r"ddos",
        r"sql injection",
        r"steal",
        r"backdoor"
    ]
    lowered = user_input.lower()
    for pat in patterns:
        if re.search(pat, lowered):
            return False
    return True

#ef pii_filter(user_input: str) -> str:
def is_technical_query(user_input: str) -> bool:
    """
    Detect if the query is technical/academic in nature and should skip PII checks.
    """
    technical_indicators = [
        # AI/ML terms
        "transformer", "attention", "neural network", "deep learning", "machine learning",
        "llm", "gpt", "bert", "model", "training", "inference", "embedding", "vector",
        "optimization", "gradient", "backpropagation", "fine-tuning", "pre-training",
        "reinforcement learning", "supervised learning", "unsupervised learning",

        # Technical architectures
        "architecture", "framework", "algorithm", "methodology", "approach",
        "technique", "implementation", "system", "pipeline", "workflow",

        # Research/Academic terms
        "research", "paper", "study", "analysis", "evaluation", "benchmark",
        "experiment", "dataset", "performance", "accuracy", "precision", "recall",

        # Specific AI models/concepts
        "deepseek", "mixtral", "llama", "claude", "gemini", "chatgpt", "openai",
        "anthropic", "huggingface", "pytorch", "tensorflow", "keras",
        "mixture-of-experts", "moe", "grpo", "policy optimization", "rlhf",
        "constitutional ai", "chain-of-thought", "few-shot", "zero-shot",

        # Technical file formats and data
        "json", "xml", "csv", "api", "database", "sql", "nosql", "vector database",
        "qdrant", "pinecone", "weaviate", "chromadb", "faiss"
    ]

    user_lower = user_input.lower()
    technical_count = sum(1 for term in technical_indicators if term in user_lower)

    # If query contains multiple technical terms, likely technical
    return technical_count >= 2

def pii_filter(user_input: str, is_technical: bool = False) -> str:
    """
    Use Presidio for PII detection, fallback to regex if unavailable.
    Never echo detected PII back to the user.
    If is_technical is True, skip all PII checks (for technical/enterprise contexts).
    """
    # Auto-detect technical queries
    if not is_technical:
        is_technical = is_technical_query(user_input)

    if is_technical:
        return ""  # Skip PII checks for technical contexts

    if presidio_analyzer:
        # Use more restrictive entity list for technical contexts
        high_confidence_entities = ["EMAIL_ADDRESS", "PHONE_NUMBER", "CREDIT_CARD", "US_SSN", "US_PASSPORT"]
        results = presidio_analyzer.analyze(text=user_input, entities=high_confidence_entities, language='en')

        # Filter out low-confidence results and technical false positives
        filtered_results = []
        for result in results:
            detected_text = user_input[result.start:result.end]

            # Skip common technical false positives
            if result.entity_type in ["DRIVER_LICENSE", "MEDICAL_LICENSE", "TAX_ID"] and len(detected_text) <= 3:
                continue  # Skip short codes like "R1", "V3", etc.

            # Skip if confidence is too low
            if result.score < 0.8:
                continue

            filtered_results.append(result)

        if filtered_results:
            return "Personal or sensitive information detected."
    # Fallback: regex for common PII (only for non-technical queries)
    if not is_technical:
        # Email
        if re.search(r"[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+", user_input):
            return "Personal or sensitive information detected."
        # US Phone Number
        if re.search(r"\b\d{3}[-.\s]?\d{3}[-.\s]?\d{4}\b", user_input):
            return "Personal or sensitive information detected."
        # US SSN
        if re.search(r"\b\d{3}-\d{2}-\d{4}\b", user_input):
            return "Personal or sensitive information detected."
        # Credit Card Numbers (normalize input: remove spaces and dashes)
        normalized = user_input.replace(' ', '').replace('-', '')
        # Visa: 4xxx xxxx xxxx xxxx
        if re.search(r"\b4[0-9]{12}(?:[0-9]{3})?\b", normalized):
            return "Personal or sensitive information detected."
        # MasterCard: 51-55, 2221-2720
        if re.search(r"\b(5[1-5][0-9]{14}|2(2[2-9][0-9]{12}|[3-6][0-9]{13}|7[01][0-9]{12}|720[0-9]{12}))\b", normalized):
            return "Personal or sensitive information detected."
        # Amex: 34/37
        if re.search(r"\b3[47][0-9]{13}\b", normalized):
            return "Personal or sensitive information detected."
        # Discover: 6011, 65, 644-649
        if re.search(r"\b(6011[0-9]{12}|65[0-9]{14}|64[4-9][0-9]{13})\b", normalized):
            return "Personal or sensitive information detected."
        # Generic: 13-19 digit number (catch-all, but not too aggressive)
        if re.search(r"\b\d{13,19}\b", normalized):
            return "Personal or sensitive information detected."
    return ""


def tool_is_high_risk(tool_name: str) -> bool:
    """
    Returns True if the tool is high risk.
    """
    return TOOL_RISKS.get(tool_name, 'low') == 'high'

def rules_based_protections(user_input: str) -> bool:
    """
    Simple deterministic protections: blocklist, input length, SQL/command injection regex.
    """
    if len(user_input) > 2000:
        return False
    for bad_word in UNSAFE_WORDS:
        # Use word boundaries to avoid false positives (e.g., "fat" in "father")
        pattern = r'\b' + re.escape(bad_word) + r'\b'
        if re.search(pattern, user_input.lower()):
            return False
    # Example SQL injection pattern
    if re.search(r"(;|--|\b(SELECT|UPDATE|DELETE|INSERT|DROP|ALTER|CREATE|EXEC|UNION|GRANT|REVOKE)\b)", user_input, re.IGNORECASE):
        return False
    return True

def clean_llm_output(text: str) -> str:
    """Clean up the LLM response to remove unwanted content."""
    text = re.sub(r"<think>.*?</think>", "", text, flags=re.DOTALL)
    text = re.sub(r"Sorry, no relevant context.*", "", text, flags=re.IGNORECASE)
    return text.strip()


def relevance_classifier(user_input: str, allowed_domains: Optional[list] = None) -> bool:
    """
    Flags off-topic queries. If allowed_domains is provided, input must mention one of them.
    """
    if not allowed_domains:
        return True  # No restriction
    lowered = user_input.lower()
    for domain in allowed_domains:
        if domain.lower() in lowered:
            return True
    return False

def tool_safeguards(tool_name: str, risk_rating: str = "low") -> bool:
    """
    Stub for tool safeguard checks. In a real agent, use this to restrict or escalate high-risk tool calls.
    For now, always allow. Extend as needed.
    """
    # Example: if risk_rating == 'high': return False
    return True

def moderation_filter(user_input: str, openai_api_key: Optional[str] = None) -> str:
    """
    Use OpenAI Moderation API for content moderation if available, else fallback to keywords.
    """
    api_key = openai_api_key or OPENAI_API_KEY
    if openai and api_key:
        try:
            openai.api_key = api_key
            response = openai.Moderation.create(input=user_input)
            results = response["results"][0]
            if results["flagged"]:
                for category, flagged in results["categories"].items():
                    if flagged:
                        return f"OpenAI Moderation flagged: {category}"
        except Exception as e:
            pass  # Fallback to keywords
    # Fallback: keyword-based
    categories = {
        "hate": ["hate", "racist", "bigot", "nazi", "white power"],
        "violence": ["kill", "murder", "shoot", "attack", "bomb", "hack", "exploit", "breach", "phishing", "malware", "ddos", "steal", "backdoor"],
        "explicit": ["sex", "porn", "nude", "xxx", "rape"],
        "self-harm": ["suicide", "kill myself", "cut myself", "self harm", "overdose"]
    }
    lowered = user_input.lower()
    for cat, keywords in categories.items():
        for kw in keywords:
            if kw in lowered:
                return f"{cat.title()} keyword detected: '{kw}'"
    return ""

def toxic_bert_check(user_input: str) -> str:
    """
    Use Toxic-BERT to flag toxic language if available.
    """
    if toxic_classifier:
        try:
            result = toxic_classifier(user_input)
            if result and result[0]['label'] == 'toxic' and result[0]['score'] > 0.5:
                return "Toxic language detected"
        except Exception:
            pass
    return ""

def run_guardrails(user_input: str, allowed_domains: Optional[list] = None, brand_terms: Optional[list] = None, openai_api_key: Optional[str] = None) -> Tuple[bool, str]:
    """
    Hybrid guardrail: Use real NLP models/APIs and rules. If unsafe, return (False, explanation_from_llm). If safe, return (True, cleaned_input).
    """
    # Check if this is a technical query first
    is_technical = is_technical_query(user_input)

    # 1. Prompt injection/safety
    if not safety_classifier(user_input):
        return False, llm_explain_block("unsafe_code", user_input)
    # 2. PII (with technical context awareness)
    pii_found = pii_filter(user_input, is_technical=is_technical)
    if pii_found:
        return False, llm_explain_block("pii", user_input)
    # 3. Moderation (OpenAI)
    mod_cat = moderation_filter(user_input, openai_api_key=openai_api_key)
    if mod_cat:
        return False, llm_explain_block("moderation", user_input)
    # 4. Toxicity (Toxic-BERT)
    toxic_reason = toxic_bert_check(user_input)
    if toxic_reason:
        return False, llm_explain_block("toxic", user_input)
    # 5. Rules-based protections
    if not rules_based_protections(user_input):
        # Could be length or restricted words
        if len(user_input) > 2000:
            return False, llm_explain_block("too_long", user_input)
        return False, llm_explain_block("unsafe_code", user_input)
    # 6. Relevance (off-topic) -- moved after all other checks
    if not relevance_classifier(user_input, allowed_domains):
        return False, llm_explain_block("off_topic", user_input)
    # 7. Output validation (brand/content) can be checked after LLM output
    # If all checks pass, input is safe
    return True, clean_llm_output(user_input)

def output_guardrails(llm_output: str) -> Tuple[bool, str]:
    """
    Apply moderation and PII filters to LLM output. Returns (is_safe, result).
    If unsafe, returns (False, explanation). If safe, returns (True, cleaned_output).
    """
    # Check moderation
    mod_reason = moderation_filter(llm_output)
    if mod_reason:
        return False, llm_explain_block("moderation", llm_output)
    # Check PII
    pii_reason = pii_filter(llm_output)
    if pii_reason:
        return False, llm_explain_block("pii", llm_output)
    return True, llm_output


def llm_explain_block(reason: str, user_input: str) -> str:
    """
    Returns a concise, professional, and privacy-preserving explanation for why input/output was blocked.
    """
    responses = {
        # PII (Personally Identifiable Information)
        "pii": "🔐It looks like your query may contain personal or sensitive details.\nTo protect your privacy, we don’t process queries that include such content.\n✅ Please rephrase your query without sharing any private information, and we’ll be happy to help.",
        # Toxic or Harmful Language
        "toxic": "⚠️ Toxic or Harmful Language\n🚫 I noticed that your query might include language that could come across as harmful or offensive.\nLet’s keep things respectful — I’m here to help in a supportive and safe space.\n✅ Try rephrasing your query with a positive or neutral tone so we can continue.",
        # Unsafe Content (Moderation)
        "moderation": "🛑 Flagged Unsafe Content\n🚫 Your query has been flagged by our safety system as potentially unsafe or sensitive.\nWe want to ensure a respectful and secure environment for all users.\n✅ Please rephrasing your query— I’m happy to help however I can.",
        # Unsafe Input Detected (e.g., SQL Injection, code)
        "unsafe_code": "🚫 Unsafe Input Detected\n⚠️ Your query seems to include patterns that resemble code or system commands, which we can’t process for safety reasons.\nProtecting this system and your data is our priority.\n✅ Kindly reword your query without technical commands so I can better assist you.",
        # Off-topic or Irrelevant Query
        "off_topic": "🤔 Off-topic or Irrelevant Query\nℹ️ It seems like your query might not align with the current support topic or context. To help you more effectively\n✅ Please rephrase your query to match the topic you’re exploring.",
        # Tool marked high risk
        "unsafe_tool": "🔒 Tool Marked as High Risk\n⚠️ The tool you selected is currently marked as high risk and isn’t supported in this environment.\nWe prioritize safe and trustworthy tools to ensure the best experience.\n✅ You can try a different tool, and I’ll be ready to help with that one.",
        # Too long
        "too_long": "⚠️ Your message is too long or complex for us to process at once. Please shorten or simplify your question and submit it again.",
        # Blacklist word
        "blacklist": "🚫 Blacklist Word Detected\n🚫 Your query contains terms that are considered sensitive or potentially harmful.\nWe aim to keep this space respectful and safe for everyone.\n✅ Let’s try rewording your query in a safer and more constructive way — I’m here to help.",
        # Default fallback
        "default": "I'm unable to process your request due to safety or content guidelines. Please rephrase and try again."
    }
    return responses.get(reason, responses["default"])
