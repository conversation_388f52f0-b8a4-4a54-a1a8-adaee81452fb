# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore
docker-compose*.yml

# Documentation
README.md
*.md
docs/

# Test files
test_*.py
*_test.py
tests/
.pytest_cache/
.coverage
htmlcov/

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp

# Environment files (will be set via environment variables)
.env
.env.*

# Dynamic generated files
dynamic_servers/
uploads/

# Development tools
.flake8
.black
.mypy_cache/
.bandit

# <PERSON><PERSON><PERSON> notebooks
*.ipynb
.ipynb_checkpoints/

# Database files
*.db
*.sqlite
*.sqlite3

# Redis dump
dump.rdb

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Backup files
*.bak
*.backup
