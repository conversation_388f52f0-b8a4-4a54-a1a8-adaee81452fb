#!/usr/bin/env python3
"""
Aggregator Agent - Multi-Agent RAG Architecture
Coordinates with sub-agents (<PERSON>dra<PERSON> and <PERSON><PERSON>) following the diagram flow
"""
import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from server.qdrant_agent import query_qdrant_tool
from server.serper_agent import query_google_search
from server.dynamic_agent_manager import query_user_dynamic_agent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AggregatorAgent")

class AggregatorAgent:
    """
    Main aggregator agent that coordinates with sub-agents
    Follows the Multi-Agent RAG architecture diagram
    """
    
    def __init__(self, memory_manager=None, planning_manager=None):
        self.memory_manager = memory_manager
        self.planning_manager = planning_manager
        self.sub_agents = {
            "qdrant": self._query_qdrant_agent,
            "serper": self._query_serper_agent
        }
        logger.info("Aggregator Agent initialized")
    
    async def _query_multiple_qdrant_collections(self, query: str, collections: List[str]) -> Dict[str, Any]:
        """Query multiple Qdrant collections and combine results"""
        try:
            logger.info(f"Querying Qdrant agent with: {query} across collections: {collections}")

            all_documents = []
            all_context_items = []
            best_score = 0.0
            combined_results = []
            collections_searched = []

            for collection_name in collections:
                try:
                    logger.info(f"Searching collection: {collection_name}")
                    result = await query_qdrant_tool(query, collection_name)
                    collections_searched.append(collection_name)

                    if isinstance(result, str) and result.strip():
                        combined_results.append(f"=== Results from {collection_name} ===")
                        combined_results.append(result)

                        # Parse individual collection results
                        lines = result.split('\n')
                        for line in lines:
                            line = line.strip()
                            if not line:
                                continue

                            # Enhanced parsing for new format: "text [Score: X.XXX] Source: file | Collection: collection"
                            if '[Score:' in line and 'Source:' in line:
                                try:
                                    # Extract score
                                    score_start = line.find('[Score:') + 7
                                    score_end = line.find(']', score_start)
                                    score_str = line[score_start:score_end].strip()
                                    score = float(score_str) if score_str != 'N/A' else 0.0
                                    best_score = max(best_score, score)

                                    # Extract source file and collection
                                    source_part = line.split('Source:')[1].strip()
                                    if '|' in source_part and 'Collection:' in source_part:
                                        source_file = source_part.split('|')[0].strip()
                                        collection_part = source_part.split('Collection:')[1].strip()
                                    else:
                                        source_file = source_part
                                        collection_part = collection_name

                                    # Extract content (everything before [Score:)
                                    content = line.split('[Score:')[0].strip()
                                    # Remove numbering if present (e.g., "1. content" -> "content")
                                    if content and content[0].isdigit() and '. ' in content:
                                        content = content.split('. ', 1)[1] if '. ' in content else content

                                    # Add to documents with detailed info
                                    all_documents.append({
                                        "file": source_file,
                                        "collection": collection_part,
                                        "score": score,
                                        "content_preview": content[:100] + "..." if len(content) > 100 else content
                                    })

                                    # Add to context items
                                    all_context_items.append(content)

                                    logger.info(f"Parsed result from {collection_name}: score={score:.3f}, file={source_file}")

                                except Exception as parse_error:
                                    logger.warning(f"Error parsing result line from {collection_name}: '{line}': {parse_error}")
                                    continue

                except Exception as collection_error:
                    logger.warning(f"Error querying collection {collection_name}: {collection_error}")
                    continue

            # Sort documents by score (highest first) and take top results
            all_documents = sorted(all_documents, key=lambda x: x["score"], reverse=True)

            # Implement intelligent relevance filtering based on score gaps
            used_documents = []
            used_context_items = []

            if all_documents:
                best_score = all_documents[0]["score"]
                logger.info(f"Best document score: {best_score:.3f}")

                # Dynamic filtering based on score quality and gaps
                for doc in all_documents:
                    content = doc["content_preview"].replace("...", "")
                    score = doc["score"]

                    # Basic quality checks
                    min_content_length = 20
                    has_meaningful_content = (
                        content.strip() and
                        len(content.strip()) > min_content_length and
                        not content.strip().lower().startswith("error") and
                        not content.strip().lower().startswith("failed")
                    )

                    if not has_meaningful_content:
                        logger.debug(f"Filtered out document (poor content): {doc['file']} (score: {score:.3f})")
                        continue

                    # Enhanced content relevance check with context-aware matching
                    query_words = set(query.lower().split())
                    content_lower = content.lower()
                    content_words = set(content_lower.split())

                    # Context-aware relevance scoring
                    relevance_score = 0
                    matched_terms = []

                    for query_word in query_words:
                        if len(query_word) >= 3:  # Only check meaningful words
                            # Skip common words that don't add relevance
                            if query_word in ['the', 'about', 'tell', 'me', 'what', 'how', 'when', 'where', 'why']:
                                continue

                            # Check for exact word matches
                            if query_word in content_words:
                                relevance_score += 2
                                matched_terms.append(query_word)
                            else:
                                # Check for substring matches with context
                                for content_word in content_words:
                                    if len(content_word) >= 4:
                                        if query_word in content_word or content_word in query_word:
                                            # Additional context check: ensure it's not a random substring
                                            if len(query_word) >= 4 and len(content_word) >= 4:
                                                relevance_score += 1
                                                matched_terms.append(f"{query_word}~{content_word}")
                                                break

                    # Determine if content is truly relevant
                    # Require higher relevance score for secondary documents
                    min_relevance = 2 if score == best_score else 3
                    query_terms_in_content = relevance_score >= min_relevance

                    # Intelligent score-based filtering with stricter thresholds
                    should_include = False
                    filter_reason = ""

                    # Always include the best document if it meets minimum quality and has query relevance
                    if score == best_score and score >= 0.1:
                        should_include = True
                        filter_reason = "best_score"

                    # Include high-quality documents (score >= 0.4) with query relevance
                    elif score >= 0.4 and query_terms_in_content:
                        should_include = True
                        filter_reason = "high_quality"

                    # Include documents within reasonable range of best score - made more strict
                    elif best_score >= 0.3 and score >= (best_score * 0.75) and query_terms_in_content:  # Within 75% of best score
                        should_include = True
                        filter_reason = "within_range"

                    # For lower best scores, be more strict and require query relevance
                    elif best_score < 0.3 and score >= max(0.2, best_score * 0.8) and query_terms_in_content:  # Within 80% of best score, min 0.2
                        should_include = True
                        filter_reason = "lenient_range"

                    # Additional check: if there's a significant score gap (>0.15), be more selective
                    elif best_score - score > 0.15 and score < 0.25:
                        should_include = False
                        filter_reason = "significant_gap"

                    # Final relevance check: if no query terms found in content, exclude unless it's the best score
                    if not query_terms_in_content and score != best_score:
                        should_include = False
                        filter_reason = "no_query_relevance"

                    # Limit total documents to prevent information overload
                    if should_include and len(used_documents) < 5:
                        # Add relevance info to document for best fit selection
                        doc_with_relevance = {
                            **doc,
                            "relevance_score": relevance_score,
                            "query_terms_in_content": query_terms_in_content,
                            "matched_terms": matched_terms
                        }
                        used_documents.append(doc_with_relevance)
                        used_context_items.append(content)
                        logger.info(f"✅ Included document: {doc['file']} (score: {score:.3f}, reason: {filter_reason}, query_relevance: {query_terms_in_content})")
                    else:
                        if should_include:
                            logger.debug(f"Skipped document (limit reached): {doc['file']} (score: {score:.3f})")
                        else:
                            logger.info(f"❌ Filtered out document: {doc['file']} (score: {score:.3f}, best: {best_score:.3f}, reason: {filter_reason})")

            # Fallback: if no documents were included but we have a reasonable best score, include the best document
            if len(used_documents) == 0 and all_documents and best_score >= 0.2:
                best_doc = all_documents[0]  # all_documents are sorted by score
                best_content = best_doc["content_preview"].replace("...", "")
                used_documents.append(best_doc)
                used_context_items.append(best_content)
                logger.info(f"🔄 Fallback: Included best document: {best_doc['file']} (score: {best_score:.3f}, reason: fallback_best_score)")

            # Create combined result string - only from documents that will be used
            if used_context_items:
                # Create a clean result string that matches what will be sent to LLM
                formatted_results = []
                for i, (doc, content) in enumerate(zip(used_documents, used_context_items), 1):
                    formatted_results.append(f"{i}. {content} [Score: {doc['score']:.3f}] Source: {doc['file']} | Collection: {doc['collection']}")
                combined_result = "\n".join(formatted_results)
            else:
                combined_result = ""

            has_relevant_results = len(used_documents) > 0

            # Select the best fit document based on content relevance, not just score
            best_fit_document = None
            if used_documents:
                # Find document with highest relevance score, then highest similarity score
                best_relevance = -1
                best_score_for_relevance = -1

                for doc in used_documents:
                    doc_relevance = doc.get("relevance_score", 0)
                    doc_score = doc.get("score", 0)

                    # Prioritize relevance score, then similarity score
                    if (doc_relevance > best_relevance or
                        (doc_relevance == best_relevance and doc_score > best_score_for_relevance)):
                        best_relevance = doc_relevance
                        best_score_for_relevance = doc_score
                        best_fit_document = doc

                # If no document has relevance info, use the first one (highest score)
                if best_fit_document is None:
                    best_fit_document = used_documents[0]

            logger.info(f"Multi-collection search complete: {len(used_documents)} documents will be used from {len(collections_searched)} collections, best_score={best_score:.3f}")
            if best_fit_document:
                logger.info(f"🎯 Best fit document: {best_fit_document['file']} from {best_fit_document['collection']} (relevance: {best_fit_document.get('relevance_score', 0)}, score: {best_fit_document['score']:.3f})")

            return {
                "success": True,
                "result": combined_result,
                "score": best_score,
                "documents": [best_fit_document] if best_fit_document else [],  # Only the best fit document
                "source": "qdrant",
                "collections_used": collections_searched,
                "has_relevant_results": has_relevant_results,
                "context_items": used_context_items,  # Only context that will be used
                "total_documents_found": len(used_documents),
                "best_fit_document": best_fit_document
            }

        except Exception as e:
            logger.error(f"Error querying multiple Qdrant collections: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "score": 0.0,
                "documents": [],
                "source": "qdrant",
                "collections_used": collections,
                "has_relevant_results": False,
                "context_items": [],
                "total_documents_found": 0
            }

    async def _query_qdrant_agent(self, query: str, collection_name: str = None) -> Dict[str, Any]:
        """Query the Qdrant sub-agent with enhanced source tracking (single collection)"""
        try:
            logger.info(f"Querying Qdrant agent with: {query} in collection: {collection_name}")
            result = await query_qdrant_tool(query, collection_name)

            # Parse the result to extract structured data with enhanced source tracking
            if isinstance(result, str):
                lines = result.split('\n')
                documents = []
                best_score = 0.0
                context_items = []

                for line in lines:
                    line = line.strip()
                    if not line:
                        continue

                    # Enhanced parsing for new format: "text [Score: X.XXX] Source: file | Collection: collection"
                    if '[Score:' in line and 'Source:' in line:
                        try:
                            # Extract score
                            score_start = line.find('[Score:') + 7
                            score_end = line.find(']', score_start)
                            score_str = line[score_start:score_end].strip()
                            score = float(score_str) if score_str != 'N/A' else 0.0
                            best_score = max(best_score, score)

                            # Extract source file and collection
                            source_part = line.split('Source:')[1].strip()
                            if '|' in source_part and 'Collection:' in source_part:
                                source_file = source_part.split('|')[0].strip()
                                collection_part = source_part.split('Collection:')[1].strip()
                            else:
                                source_file = source_part
                                collection_part = collection_name or "Gen AI"

                            # Extract content (everything before [Score:)
                            content = line.split('[Score:')[0].strip()
                            # Remove numbering if present (e.g., "1. content" -> "content")
                            if content and content[0].isdigit() and '. ' in content:
                                content = content.split('. ', 1)[1] if '. ' in content else content

                            # Add to documents with detailed info
                            documents.append({
                                "file": source_file,
                                "collection": collection_part,
                                "score": score,
                                "content_preview": content[:100] + "..." if len(content) > 100 else content
                            })

                            # Add to context items
                            context_items.append(content)

                            logger.info(f"Parsed Qdrant result: score={score:.3f}, file={source_file}, collection={collection_part}")

                        except Exception as parse_error:
                            logger.warning(f"Error parsing Qdrant result line '{line}': {parse_error}")
                            continue

                # Sort documents by score (highest first) for intelligent filtering
                documents = sorted(documents, key=lambda x: x["score"], reverse=True)

                # Implement intelligent relevance filtering based on score gaps
                used_documents = []
                used_context_items = []

                if documents:
                    best_score = documents[0]["score"]
                    logger.info(f"Best document score: {best_score:.3f}")

                    # Dynamic filtering based on score quality and gaps
                    for doc in documents:
                        content = doc["content_preview"].replace("...", "")
                        score = doc["score"]

                        # Basic quality checks
                        min_content_length = 20
                        has_meaningful_content = (
                            content.strip() and
                            len(content.strip()) > min_content_length and
                            not content.strip().lower().startswith("error") and
                            not content.strip().lower().startswith("failed")
                        )

                        if not has_meaningful_content:
                            logger.debug(f"Filtered out document (poor content): {doc['file']} (score: {score:.3f})")
                            continue

                        # Enhanced content relevance check with context-aware matching
                        query_words = set(query.lower().split())
                        content_lower = content.lower()
                        content_words = set(content_lower.split())

                        # Context-aware relevance scoring
                        relevance_score = 0
                        matched_terms = []

                        for query_word in query_words:
                            if len(query_word) >= 3:  # Only check meaningful words
                                # Check for exact word matches
                                if query_word in content_words:
                                    relevance_score += 2
                                    matched_terms.append(query_word)
                                else:
                                    # Check for substring matches with context
                                    for content_word in content_words:
                                        if len(content_word) >= 4:
                                            if query_word in content_word or content_word in query_word:
                                                # Additional context check: ensure it's not a random substring
                                                if len(query_word) >= 4 and len(content_word) >= 4:
                                                    relevance_score += 1
                                                    matched_terms.append(f"{query_word}~{content_word}")
                                                    break

                        # Determine if content is truly relevant
                        # Require higher relevance score for secondary documents, but be reasonable
                        if score == best_score:
                            min_relevance = 2  # Best document needs moderate relevance
                        elif score >= best_score * 0.85:
                            min_relevance = 2  # Very close scores get same treatment
                        else:
                            min_relevance = 3  # Lower scores need stronger relevance

                        query_terms_in_content = relevance_score >= min_relevance



                        # Intelligent score-based filtering with stricter thresholds
                        should_include = False
                        filter_reason = ""

                        # Always include the best document if it meets minimum quality and has query relevance
                        if score == best_score and score >= 0.1:
                            should_include = True
                            filter_reason = "best_score"

                        # Include high-quality documents (score >= 0.4) with query relevance
                        elif score >= 0.4 and query_terms_in_content:
                            should_include = True
                            filter_reason = "high_quality"

                        # Include documents within reasonable range of best score - made more strict
                        elif best_score >= 0.3 and score >= (best_score * 0.75) and query_terms_in_content:  # Within 75% of best score
                            should_include = True
                            filter_reason = "within_range"

                        # For lower best scores, be more strict and require query relevance
                        elif best_score < 0.3 and score >= max(0.2, best_score * 0.8) and query_terms_in_content:  # Within 80% of best score, min 0.2
                            should_include = True
                            filter_reason = "lenient_range"

                        # Additional check: if there's a significant score gap (>0.15), be more selective
                        elif best_score - score > 0.15 and score < 0.25:
                            should_include = False
                            filter_reason = "significant_gap"

                        # Final relevance check: if no query terms found in content, exclude unless it's the best score
                        if not query_terms_in_content and score != best_score:
                            should_include = False
                            filter_reason = "no_query_relevance"

                        # Limit total documents to prevent information overload
                        if should_include and len(used_documents) < 5:
                            # Add relevance info to document for best fit selection
                            doc_with_relevance = {
                                **doc,
                                "relevance_score": relevance_score,
                                "query_terms_in_content": query_terms_in_content,
                                "matched_terms": matched_terms
                            }
                            used_documents.append(doc_with_relevance)
                            used_context_items.append(content)
                            logger.info(f"✅ Included document: {doc['file']} (score: {score:.3f}, reason: {filter_reason}, query_relevance: {query_terms_in_content})")
                        else:
                            if should_include:
                                logger.debug(f"Skipped document (limit reached): {doc['file']} (score: {score:.3f})")
                            else:
                                logger.info(f"❌ Filtered out document: {doc['file']} (score: {score:.3f}, best: {best_score:.3f}, reason: {filter_reason})")

                # Fallback: if no documents were included but we have a reasonable best score, include the best document
                if len(used_documents) == 0 and best_score >= 0.2:
                    best_doc = documents[0]  # documents are sorted by score
                    best_content = best_doc["content_preview"].replace("...", "")
                    used_documents.append(best_doc)
                    used_context_items.append(best_content)
                    logger.info(f"🔄 Fallback: Included best document: {best_doc['file']} (score: {best_score:.3f}, reason: fallback_best_score)")

                has_relevant_results = len(used_documents) > 0

                # Select the best fit document based on content relevance, not just score
                best_fit_document = None
                if used_documents:
                    # Find document with highest relevance score, then highest similarity score
                    best_relevance = -1
                    best_score_for_relevance = -1

                    for doc in used_documents:
                        doc_relevance = doc.get("relevance_score", 0)
                        doc_score = doc.get("score", 0)

                        # Prioritize relevance score, then similarity score
                        if (doc_relevance > best_relevance or
                            (doc_relevance == best_relevance and doc_score > best_score_for_relevance)):
                            best_relevance = doc_relevance
                            best_score_for_relevance = doc_score
                            best_fit_document = doc

                    # If no document has relevance info, use the first one (highest score)
                    if best_fit_document is None:
                        best_fit_document = used_documents[0]

                logger.info(f"Qdrant parsing complete: {len(used_documents)} documents will be used, best_score={best_score:.3f}, has_relevant={has_relevant_results}")
                if best_fit_document:
                    logger.info(f"🎯 Best fit document: {best_fit_document['file']} from {best_fit_document['collection']} (relevance: {best_fit_document.get('relevance_score', 0)}, score: {best_fit_document['score']:.3f})")

                return {
                    "success": True,
                    "result": result,
                    "score": best_score,
                    "documents": [best_fit_document] if best_fit_document else [],  # Only the best fit document
                    "source": "qdrant",
                    "collection_used": collection_name or "Gen AI",
                    "collections_used": [collection_name or "Gen AI"],
                    "has_relevant_results": has_relevant_results,
                    "context_items": used_context_items,  # Only context that will be used
                    "total_documents_found": len(used_documents),
                    "best_fit_document": best_fit_document
                }

            return {
                "success": True,
                "result": str(result),
                "score": 0.0,
                "documents": [],
                "source": "qdrant",
                "collection_used": collection_name or "Gen AI",
                "collections_used": [collection_name or "Gen AI"],
                "has_relevant_results": False,
                "context_items": [],
                "total_documents_found": 0
            }

        except Exception as e:
            logger.error(f"Error querying Qdrant agent: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "score": 0.0,
                "documents": [],
                "source": "qdrant",
                "collection_used": collection_name or "Gen AI",
                "collections_used": [collection_name or "Gen AI"],
                "has_relevant_results": False,
                "context_items": [],
                "total_documents_found": 0
            }
    
    async def _query_serper_agent(self, query: str, num_results: int = 3) -> Dict[str, Any]:
        """Query the Serper sub-agent with enhanced URL tracking"""
        try:
            logger.info(f"Querying Serper agent with: {query} (max {num_results} results)")
            result = await query_google_search(query, num_results)

            # Extract URLs and titles from the result with better parsing
            source_urls = []
            web_sources = []

            if isinstance(result, str):
                lines = result.split('\n')
                current_item = {}
                for line in lines:
                    line = line.strip()
                    if line.startswith('🔎 **') and line.endswith('**'):
                        # Extract title
                        title = line.replace('🔎 **', '').replace('**', '').strip()
                        current_item = {"title": title}
                    elif line.startswith('🔗 '):
                        # Extract URL
                        url = line.replace('🔗 ', '').strip()
                        if url.startswith('http') and len(source_urls) < 3:  # Limit to 3 URLs
                            source_urls.append(url)
                            if current_item:
                                current_item["url"] = url
                                web_sources.append(current_item)
                                current_item = {}

            # Ensure we have at most 3 URLs
            source_urls = source_urls[:3]
            web_sources = web_sources[:3]

            return {
                "success": True,
                "result": result,
                "source_urls": source_urls,
                "web_sources": web_sources,
                "source": "web",
                "total_results_found": len(source_urls)
            }

        except Exception as e:
            logger.error(f"Error querying Serper agent: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "source_urls": [],
                "web_sources": [],
                "source": "web",
                "total_results_found": 0
            }


    async def coordinate_query(self, query: str, mode: str = "agentic", 
                             collections: List[str] = None, user_id: str = None, **kwargs) -> Dict[str, Any]:
        """
        Enhanced Master Agent coordination with specialized dynamic agents
        Implements the Multi-Agent RAG architecture from diagram

        Main coordination method that follows the Multi-Agent RAG flow
        
        Args:
            query: User query
             mode: rag, agentic, web, or dynamic_mcp
            collections: Qdrant collections to search
            **kwargs: Additional parameters
        
        Returns:
            Coordinated response from sub-agents

        """
        logger.info(f"Master Agent coordinating Query: '{query}' (with mode: {mode}, user: {user_id})")
        
        response = {
            "query": query,
            "mode": mode,
            "qdrant_result": None,
            "web_result": None,
            "dynamic_result": None,
            "final_context": [],
            "documents": [],
            "source_urls": [],
            "decision": "",
            "score": 0.0,
            "source": "",
            "decision_reason": "",
            "collections_searched": collections or ["Gen AI"],
            "web_sources": [],
            "agents_used": []
        }
        
        try:
            # Step 1: Check for user's specialized dynamic agent (ONLY for dynamic_mcp mode)
            if user_id and mode == "dynamic_mcp":
                logger.info(f"Checking for specialized agent for user {user_id} in dynamic_mcp mode")
                dynamic_response = await query_user_dynamic_agent(user_id, query, mode)
                response["dynamic_result"] = dynamic_response

                if dynamic_response["success"]:
                    response["final_context"].append(dynamic_response["result"])

                    # Enhanced source information with agent selection details
                    selected_agent_id = dynamic_response.get("selected_agent_id", "unknown")
                    selection_method = dynamic_response.get("agent_selection_method", "default")
                    response["source"] = f"dynamic_agent_{selected_agent_id}"
                    response["agent_selection_info"] = {
                        "selected_agent_id": selected_agent_id,
                        "selection_method": selection_method,
                        "intent_analysis": dynamic_response.get("intent_analysis")
                    }

                    response["score"] = 0.9  # High priority for user's own data
                    response["agents_used"].append(dynamic_response.get("agent_type", "dynamic"))

                    # If specialized agent provides good answer, prioritize it
                    if len(dynamic_response["result"]) > 50:  # Substantial response
                        response["decision"] = "specialized_agent_primary"
                        logger.info(f"Dynamic agent {selected_agent_id} provided primary response using {selection_method} selection")
                        return response
                else:
                    # Enhanced error handling with suggestions (only for dynamic_mcp mode)
                    error_msg = dynamic_response.get('error', 'Unknown error')
                    suggested_service = dynamic_response.get('suggested_service')
                    intent_analysis = dynamic_response.get('intent_analysis')

                    logger.info(f"No suitable dynamic agent found for user {user_id}: {error_msg}")
                    if suggested_service:
                        logger.info(f"Suggested service for query: {suggested_service}")

                     # Return the helpful error message for dynamic_mcp mode
                    response["decision"] = "no_suitable_agent"
                    response["source"] = "fallback"
                    response["final_context"].append(dynamic_response.get("result", error_msg))
                    response["suggested_service"] = suggested_service
                    response["intent_analysis"] = intent_analysis
                    return response
                
            
            # Step 2: Use existing Qdrant agent (Local Data Sources)
            if mode in ["rag", "agentic"]:
                # Import planning manager for intelligent collection selection
                from planning_manager import PlanningManager
                planning_manager = PlanningManager()

                # Use intelligent collection selection if no collections provided
                if not collections:
                    collections_to_search = planning_manager.select_collections_for_query(query)
                    logger.info(f"Auto-selected collections for query '{query[:50]}...': {collections_to_search}")
                else:
                    collections_to_search = collections
                    logger.info(f"Using provided collections: {collections_to_search}")

                if len(collections_to_search) == 1:
                    # Single collection - use existing method
                    qdrant_response = await self._query_qdrant_agent(query, collections_to_search[0])
                else:
                    # Multiple collections - use new multi-collection method
                    qdrant_response = await self._query_multiple_qdrant_collections(query, collections_to_search)

                response["qdrant_result"] = qdrant_response
                response["agents_used"].append("qdrant_agent")

                if qdrant_response["success"]:
                    response["score"] = max(response["score"], qdrant_response["score"])
                    response["documents"] = qdrant_response["documents"]
                    response["final_context"].append(qdrant_response["result"])

                    # Update collections_searched to reflect what was actually searched
                    if "collections_used" in qdrant_response:
                        response["collections_searched"] = qdrant_response["collections_used"]
                    else:
                        response["collections_searched"] = [qdrant_response.get("collection_used", "Gen AI")]
            
            # Step 3: Decision making based on Multi-Agent RAG architecture
            if mode == "dynamic_mcp":
                # For dynamic_mcp mode, only use dynamic agent results
                if response["dynamic_result"] and response["dynamic_result"].get("success", False):
                    response["decision"] = "dynamic_mcp_success"
                    response["source"] = "dynamic_mcp"
                    response["final_context"] = [response["dynamic_result"]["result"]]
                else:
                    response["decision"] = "dynamic_mcp_failed"
                    response["source"] = "dynamic_mcp_error"
                    response["final_context"] = ["No dynamic MCP server found or query failed"]
            elif response["dynamic_result"] and response["dynamic_result"].get("success", False):
                if response["score"] >= 0.8:
                    response["decision"] = "specialized_with_context"
                    response["source"] = "specialized+qdrant"
                else:
                    response["decision"] = "specialized_primary"
                    response["source"] = "specialized"

            elif mode == "rag":
                response["decision"] = "only_rag"
                response["source"] = "qdrant"

                # Enhanced relevance check for RAG mode
                if response["final_context"] and response["documents"]:
                    context_text = " ".join(response["final_context"]).lower()
                    query_lower = query.lower()

                    # Check document metadata for content type
                    doc_files = [doc.get('file', '').lower() for doc in response["documents"]]
                    doc_text = " ".join(doc_files)

                    # Enhanced relevance indicators
                    ai_indicators = ['deepseek', 'attention', 'transformer', 'neural', 'model', 'ai', 'llm', 'bert', 'gpt']
                    programming_indicators = ['react', 'javascript', 'js', 'html', 'css', 'web', 'frontend', 'handbook']

                    # Query type detection
                    programming_queries = [
                        'javascript', 'js', 'react', 'promise', 'async', 'function', 'method',
                        'catch', 'then', 'callback', 'event', 'dom', 'html', 'css', 'web',
                        'frontend', 'backend', 'api', 'fetch', 'ajax'
                    ]
                    ai_queries = [
                        'machine learning', 'deep learning', 'neural', 'transformer', 'attention',
                        'ai', 'artificial intelligence', 'model', 'training', 'inference'
                    ]
                    non_technical_queries = [
                        'weather', 'temperature', 'cook', 'pasta', 'recipe', 'stock', 'price',
                        'iphone', 'phone', 'mobile', 'news', 'sports', 'entertainment'
                    ]

                    docs_are_ai = any(indicator in doc_text for indicator in ai_indicators)
                    docs_are_programming = any(indicator in doc_text for indicator in programming_indicators)

                    query_is_programming = any(term in query_lower for term in programming_queries)
                    query_is_ai = any(term in query_lower for term in ai_queries)
                    query_is_non_technical = any(term in query_lower for term in non_technical_queries)

                    logger.info(f"[RAG] Relevance check: docs_ai={docs_are_ai}, docs_prog={docs_are_programming}")
                    logger.info(f"[RAG] Query type: prog={query_is_programming}, ai={query_is_ai}, non_tech={query_is_non_technical}")
                    logger.info(f"[RAG] Doc files: {doc_files}")
                    logger.info(f"[RAG] Query: {query_lower}")

                    # Check for mismatched content types
                    is_mismatch = (
                        (docs_are_ai and query_is_programming and not query_is_ai) or
                        (docs_are_programming and query_is_ai and not query_is_programming) or
                        (query_is_non_technical and (docs_are_ai or docs_are_programming))
                    )

                    # If there's a clear mismatch, filter out irrelevant documents
                    if is_mismatch:
                        response["documents"] = []
                        response["final_context"] = ["Sorry, no relevant information was found in the provided documents."]
                        response["score"] = 0.0
                        logger.info(f"[RAG] Detected content type mismatch for query '{query}', returning fallback")
            elif mode == "agentic":
                # Fixed agentic logic: use score threshold of 0.3
                if response["score"] >= 0.3:
                    response["decision"] = "good_enough"
                    response["source"] = "qdrant"
                    response["source_urls"] = []  # Clear any web URLs
                    # Keep only Qdrant results in final_context
                    logger.info(f"[Agentic] Score: {response['score']:.3f} -> Decision: good_enough (using Qdrant only)")
                else:
                    response["decision"] = "needs_web_search"
                    logger.info(f"[Agentic] Score: {response['score']:.3f} -> Decision: needs_web_search (using Serper only)")
                    # Use Serper agent for web search
                    web_response = await self._query_serper_agent(query)
                    response["web_result"] = web_response
                    #response["agents_used"].append("serper_agent")
                    response["agents_used"] = ["serper_agent"]  # Only use Serper agent


                    if web_response["success"]:
                        response["final_context"] = [web_response["result"]]  # Use only web results
                        response["source_urls"] = web_response.get("source_urls", [])
                        response["source"] = "web"
                        response["documents"] = []  # Clear Qdrant documents when using web
                    else:
                        response["source"] = "qdrant"  # Fallback to Qdrant if web fails
                        response["source_urls"] = []
            elif mode == "web":
                response["decision"] = "web_only"
                response["source"] = "web"
                # Use Serper agent for web search
                web_response = await self._query_serper_agent(query)
                response["web_result"] = web_response
                response["agents_used"].append("serper_agent")

                if web_response["success"]:
                    response["final_context"] = [web_response["result"]]
                    response["source_urls"] = web_response.get("source_urls", [])
                    response["source"] = "web"
            
            # Step 4: Final source attribution (don't override mode-specific sources)
            # Only combine sources if it's truly a multi-agent scenario
            if mode == "agentic" and len(response["agents_used"]) > 1:
                # For agentic mode, this should not happen with the new logic
                # But if it does, keep the primary source
                pass  # Source already set correctly above
            
            logger.info(f"Master Agent decision: {response['decision']} using {response['agents_used']}")
            return response
            
        except Exception as e:
            logger.error(f"Error in Master Agent coordination: {e}")
            response["decision"] = "error"
            response["final_context"] = [f"Error processing query: {str(e)}"]
            return response
    
    
    def get_memory_context(self, chat_id: str, user_id: str) -> str:
        """Get memory context from memory manager"""
        try:
            if self.memory_manager:
                return self.memory_manager.get_context(chat_id, user_id)
            return ""
        except Exception as e:
            logger.error(f"Error getting memory context: {e}")
            return ""
    
    def store_interaction(self, query: str, response: str, chat_id: str, user_id: str, metadata: Dict = None):
        """Store interaction in memory manager"""
        if self.memory_manager:
            self.memory_manager.store_interaction(query, response, chat_id, user_id, metadata)

# Global aggregator agent instance
aggregator_agent = None

def get_aggregator_agent(memory_manager=None, planning_manager=None) -> AggregatorAgent:
    """Get or create aggregator agent instance"""
    global aggregator_agent
    if aggregator_agent is None:
        aggregator_agent = AggregatorAgent(memory_manager, planning_manager)
    return aggregator_agent

async def test_aggregator_agent():
    """Test the aggregator agent with real queries"""
    print("🤖 AGGREGATOR AGENT TEST")
    print("=" * 60)

    try:
        # Initialize agent
        agent = get_aggregator_agent()
        print("✅ Aggregator agent initialized")

        # Test different modes with real queries
        test_queries = [
            {
                "query": "Who is Sundar Pichai?",
                "mode": "rag",
                "description": "RAG Mode - Should search Qdrant only"
            },
            {
                "query": "Latest news about artificial intelligence 2024",
                "mode": "web",
                "description": "Web Mode - Should search web only"
            },
            {
                "query": "What is machine learning and how does it work?",
                "mode": "agentic",
                "description": "Agentic Mode - Should try Qdrant first, then web if needed"
            },
            {
                "query": "Explain neural networks",
                "mode": "agentic",
                "description": "Agentic Mode - Technical query test"
            }
        ]

        for i, test_case in enumerate(test_queries, 1):
            print(f"\n🔍 Test {i}: {test_case['description']}")
            print(f"Query: '{test_case['query']}'")
            print(f"Mode: {test_case['mode']}")
            print("-" * 40)

            try:
                # Test coordination
                result = await agent.coordinate_query(
                    query=test_case['query'],
                    mode=test_case['mode'],
                    collections=["Gen AI"]
                )

                # Display results
                print(f"✅ Coordination successful!")
                print(f"   Decision: {result['decision']}")
                print(f"   Score: {result['score']:.3f}")
                print(f"   Final context items: {len(result['final_context'])}")
                print(f"   Documents found: {len(result['documents'])}")
                print(f"   Source URLs: {len(result['source_urls'])}")

                # Show first 100 chars of context if available
                if result['final_context']:
                    context_preview = result['final_context'][0][:100] + "..." if len(result['final_context'][0]) > 100 else result['final_context'][0]
                    print(f"   Context preview: {context_preview}")

                # Show documents if available
                if result['documents']:
                    print(f"   Document sources: {[doc.get('file', 'unknown') for doc in result['documents'][:3]]}")

                # Show URLs if available
                if result['source_urls']:
                    print(f"   Web sources: {len(result['source_urls'])} URLs found")

            except Exception as e:
                print(f"❌ Test failed: {str(e)}")

            print()

        # Test memory context (if available)
        print("🧠 Testing Memory Context...")
        try:
            test_chat_id = "507f1f77bcf86cd799439011"  # Sample ObjectId
            test_user_id = "user123"
            context = agent.get_memory_context(test_chat_id, test_user_id)
            print(f"✅ Memory context retrieved: {len(context)} characters")
        except Exception as e:
            print(f"⚠️ Memory context test: {str(e)}")

        print("\n" + "=" * 60)
        print("🎉 Aggregator Agent testing completed!")
        print("📝 Note: Some tests may show errors if MCP servers are not running")
        print("📝 To run MCP servers:")
        print("   - Terminal 1: python server/qdrant.py")
        print("   - Terminal 2: python server/serper.py")

    except Exception as e:
        print(f"❌ Aggregator agent test failed: {str(e)}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install fastapi uvicorn python-dotenv pymongo redis")

if __name__ == "__main__":
    print("🚀 Starting Aggregator Agent Test...")
    asyncio.run(test_aggregator_agent())
