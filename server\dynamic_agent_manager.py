#!/usr/bin/env python3
"""
Dynamic Agent Manager
Manages dynamic sub-agents that connect to user-specific MCP servers
"""
import asyncio
import json
import logging
import os
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastmcp import Client
from server.dynamic_mcp_factory import get_dynamic_mcp_server_info
from mcp.client.stdio import stdio_client
from mcp import ClientSession
from mcp import StdioServerParameters
import subprocess

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DynamicAgentManager")


class DynamicAgent:
    """Individual dynamic agent that connects to a specific MCP server"""
    
    def __init__(self, agent_id: str, user_id: str, server_id: str, agent_name: str, script_path: str):
        self.agent_id = agent_id
        self.user_id = user_id
        self.server_id = server_id
        self.agent_name = agent_name
        self.mcp_client = None
        self.created_at = datetime.now().isoformat()
        self.last_used = None
        self.script_path = script_path
        self.status = "created"
        self._connection_params = None  # Will be set during initialization
        self._factory = None  # Allow injection of factory instance
        logger.info(f"Dynamic agent created: {agent_id} for user {user_id}")
    
    async def initialize(self) -> bool:
        """Initialize the agent and connect to its MCP server"""
        try:
            # Validate server existence using the injected factory or global factory instance
            if self._factory:
                factory = self._factory
            else:
                from server.dynamic_mcp_factory import dynamic_mcp_factory
                factory = dynamic_mcp_factory

            server_info = factory.get_server_info(self.server_id)
            if not server_info:
                logger.error(f"No server info found for server {self.server_id}")
                available_servers = factory.list_servers()
                logger.info(f"Available servers: {[s['server_id'] for s in available_servers]}")
                # Try to get server info from the script path directly
                if os.path.exists(self.script_path):
                    logger.info(f"Script file exists, proceeding with initialization")
                else:
                    logger.error(f"Script file not found: {self.script_path}")
                    return False

            logger.info(f"Initializing agent {self.agent_name} for server {self.server_id}")

            # Check if script file exists
            if not os.path.exists(self.script_path):
                logger.error(f"Script file not found: {self.script_path}")
                self.status = "error"
                return False

            # Simple validation - just check if script exists and store connection params
            try:
                logger.info(f"Validating MCP server script for {self.server_id}")

                # Create server parameters
                params = StdioServerParameters(
                    command="python",
                    args=[self.script_path]
                )

                # For now, just validate the script exists and is readable
                with open(self.script_path, 'r') as f:
                    content = f.read()
                    if 'FastMCP' in content and 'mcp.run' in content:
                        logger.info(f"Successfully validated server script {self.server_id}")
                        self.status = "connected"
                        self._connection_params = params  # Store params for later connections
                        return True
                    else:
                        logger.error(f"Script {self.script_path} doesn't appear to be a valid MCP server")
                        self.status = "error"
                        return False

            except Exception as e:
                logger.error(f"Failed to validate script {self.script_path}: {e}")
                self.status = "error"
                return False

        except Exception as e:
            logger.error(f"Failed to initialize agent {self.agent_id}: {e}")
            self.status = "error"
            return False


    async def query(self, query: str, tool_name: str = None) -> Dict[str, Any]:
        """Query the agent's MCP server"""
        try:
            if self.status != "connected" or not hasattr(self, '_connection_params'):
                return {
                    "success": False,
                    "error": "Agent not initialized or connection parameters missing",
                    "result": "",
                    "agent_type": "dynamic",
                    "agent_id": self.agent_id
                }

            # Create a new connection for each query to avoid context manager issues
            try:
                logger.info(f"Attempting to connect to MCP server {self.server_id} with script {self.script_path}")

                # Verify script file exists and is readable
                if not os.path.exists(self.script_path):
                    logger.error(f"Script file not found: {self.script_path}")
                    return {
                        "success": False,
                        "error": f"Script file not found: {self.script_path}",
                        "result": "",
                        "agent_type": "dynamic",
                        "agent_id": self.agent_id
                    }

                async with stdio_client(self._connection_params) as (read_stream, write_stream):
                    logger.info(f"Successfully created stdio client for {self.server_id}")
                    async with ClientSession(read_stream, write_stream) as session:
                        logger.info(f"Created client session for {self.server_id}")
                        # Initialize session
                        await session.initialize()
                        logger.info(f"Successfully initialized session for {self.server_id}")

                        # List available tools
                        logger.info(f"Listing tools for {self.server_id}")
                        tools_result = await session.list_tools()
                        tools = tools_result.tools if hasattr(tools_result, 'tools') else []
                        logger.info(f"Found {len(tools)} tools for {self.server_id}: {[t.name for t in tools]}")

                        if not tools:
                            logger.warning(f"No tools available for {self.server_id}")
                            return {
                                "success": False,
                                "error": "No tools available",
                                "result": "",
                                "agent_type": "dynamic",
                                "agent_id": self.agent_id
                            }

                        # Use first available tool if none specified
                        if not tool_name:
                            # For MongoDB servers, intelligently choose between tools
                            if any('mongodb' in t.name.lower() for t in tools):
                                query_lower = query.lower()
                                # If asking for collections/tables/list, use list tool
                                if any(keyword in query_lower for keyword in ['collections', 'tables', 'list', 'show', 'what collections', 'what tables']):
                                    list_tool = next((t for t in tools if 'list' in t.name.lower()), None)
                                    if list_tool:
                                        tool_name = list_tool.name
                                    else:
                                        tool_name = tools[0].name
                                # If asking to inspect/analyze structure, use inspect tool
                                elif any(keyword in query_lower for keyword in ['inspect', 'structure', 'fields', 'schema', 'analyze']):
                                    inspect_tool = next((t for t in tools if 'inspect' in t.name.lower()), None)
                                    if inspect_tool:
                                        tool_name = inspect_tool.name
                                    else:
                                        # Fallback to list tool
                                        list_tool = next((t for t in tools if 'list' in t.name.lower()), None)
                                        if list_tool:
                                            tool_name = list_tool.name
                                        else:
                                            tool_name = tools[0].name
                                # For general search queries, prefer search tool
                                elif any(keyword in query_lower for keyword in ['search', 'find', 'get', 'show me', 'what', 'who']):
                                    search_tool = next((t for t in tools if 'search' in t.name.lower()), None)
                                    if search_tool:
                                        tool_name = search_tool.name
                                    else:
                                        # Fallback to query tool
                                        query_tool = next((t for t in tools if 'query' in t.name.lower()), None)
                                        if query_tool:
                                            tool_name = query_tool.name
                                        else:
                                            tool_name = tools[0].name
                                else:
                                    # For specific queries with JSON or collection names, use query tool
                                    query_tool = next((t for t in tools if 'query' in t.name.lower()), None)
                                    if query_tool:
                                        tool_name = query_tool.name
                                    else:
                                        tool_name = tools[0].name

                            # For news servers, intelligently choose between get_latest_news and search_news
                            elif any('news' in t.name.lower() for t in tools):
                                # Check if query is specific enough for search
                                if any(keyword in query.lower() for keyword in ['political', 'politics', 'india', 'election', 'government', 'specific']):
                                    # Look for search_news tool first
                                    search_news_tool = next((t for t in tools if 'search' in t.name.lower() and 'news' in t.name.lower()), None)
                                    if search_news_tool:
                                        tool_name = search_news_tool.name
                                    else:
                                        tool_name = tools[0].name
                                else:
                                    # Look for get_latest_news tool first
                                    latest_news_tool = next((t for t in tools if 'latest' in t.name.lower() or ('get' in t.name.lower() and 'news' in t.name.lower())), None)
                                    if latest_news_tool:
                                        tool_name = latest_news_tool.name
                                    else:
                                        tool_name = tools[0].name

                            # For other database servers (SQL-based)
                            elif any(db_type in t.name.lower() for t in tools for db_type in ['postgres', 'mysql', 'sql']):
                                query_lower = query.lower()
                                if any(keyword in query_lower for keyword in ['tables', 'schema', 'list', 'show tables']):
                                    # Use a general query to list tables
                                    query_tool = next((t for t in tools if 'query' in t.name.lower()), None)
                                    if query_tool:
                                        tool_name = query_tool.name
                                    else:
                                        tool_name = tools[0].name
                                else:
                                    # Use query tool for data queries
                                    query_tool = next((t for t in tools if 'query' in t.name.lower()), None)
                                    if query_tool:
                                        tool_name = query_tool.name
                                    else:
                                        tool_name = tools[0].name

                            else:
                                tool_name = tools[0].name

                        # Find the tool
                        tool = next((t for t in tools if t.name == tool_name), None)
                        if not tool:
                            # Try to find a search or query tool
                            search_tools = [t for t in tools if any(keyword in t.name.lower()
                                                                  for keyword in ['search', 'query', 'list', 'get'])]
                            if search_tools:
                                tool = search_tools[0]
                                tool_name = tool.name
                            else:
                                tool = tools[0]
                                tool_name = tool.name

                        # Prepare tool arguments based on tool name
                        tool_args = self._prepare_tool_args(tool_name, query)
                        logger.info(f"Calling tool {tool_name} with args {tool_args} for {self.server_id}")

                        # Call the tool
                        result = await session.call_tool(tool_name, tool_args)
                        logger.info(f"Tool {tool_name} executed successfully for {self.server_id}")

                        # Update last used
                        self.last_used = datetime.now().isoformat()

                        # Process result
                        if result and hasattr(result, 'content'):
                            content = result.content[0].text if result.content else str(result)
                        else:
                            content = str(result)

                        # Extract source URLs from the content
                        source_urls = self._extract_source_urls(content)

                        return {
                            "success": True,
                            "result": content,
                            "source_urls": source_urls,
                            "tool_used": tool_name,
                            "agent_type": "dynamic",
                            "agent_id": self.agent_id,
                            "server_id": self.server_id
                        }

            except Exception as mcp_error:
                logger.error(f"MCP client error for agent {self.agent_id}: {mcp_error}", exc_info=True)
                # Return error but don't crash
                return {
                    "success": False,
                    "error": f"MCP client error: {str(mcp_error)}",
                    "result": "",
                    "agent_type": "dynamic",
                    "agent_id": self.agent_id
                }

        except Exception as e:
            logger.error(f"Error querying agent {self.agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "agent_type": "dynamic",
                "agent_id": self.agent_id
            }
    
    def _prepare_tool_args(self, tool_name: str, query: str) -> Dict[str, Any]:
        """Prepare tool arguments based on tool name and query"""
        tool_name_lower = tool_name.lower()
        
        # Common argument patterns
        if 'search' in tool_name_lower:
            if 's3' in tool_name_lower or 'blob' in tool_name_lower or 'gcs' in tool_name_lower:
                return {"search_term": query}
            elif 'sharepoint' in tool_name_lower or 'onedrive' in tool_name_lower:
                return {"search_term": query}
            elif 'mongodb' in tool_name_lower:
                # For MongoDB search, extract collection name if mentioned
                collection_name, _ = self._parse_mongodb_query(query)
                return {"search_term": query, "collection_name": collection_name, "limit": 10}
            elif 'airbnb' in tool_name_lower:
                return {"location": query}
            elif 'google' in tool_name_lower:
                return {"query": query}
            elif 'news' in tool_name_lower:
                return {"query": query, "language": "en"}
            else:
                return {"search_term": query}
        
        elif 'list' in tool_name_lower:
            if 's3' in tool_name_lower or 'blob' in tool_name_lower or 'gcs' in tool_name_lower:
                return {"prefix": query if len(query) < 50 else ""}
            else:
                return {}

        elif 'inspect' in tool_name_lower:
            if 'mongodb' in tool_name_lower:
                # Extract collection name from query
                collection_name, _ = self._parse_mongodb_query(query)
                return {"collection_name": collection_name}
            else:
                return {"query": query}
        
        elif 'query' in tool_name_lower:
            if 'sql' in tool_name_lower or 'postgres' in tool_name_lower or 'mysql' in tool_name_lower:
                return {"sql_query": f"SELECT * FROM information_schema.tables WHERE table_name LIKE '%{query}%' LIMIT 10"}
            elif 'mongodb' in tool_name_lower:
                # For MongoDB, try to extract collection name and query from user input
                collection_name, mongo_query = self._parse_mongodb_query(query)
                return {"collection_name": collection_name, "query": mongo_query, "limit": 10}
            else:
                return {"query": query}
        
        elif 'get' in tool_name_lower:
            if 'weather' in tool_name_lower:
                # Extract location from weather query
                location = self._extract_location_from_weather_query(query)
                return {"location": location}
                #location = self._extract_location_from_weather_query(query)
                #return {"location": location}
            elif 'news' in tool_name_lower:
                # For news, check if query contains political/India keywords to use search_news instead
                if any(keyword in query.lower() for keyword in ['political', 'politics', 'india', 'election', 'government']):
                    # Use search_news tool for specific queries
                    return {"query": query, "language": "en"}
                else:
                    # Use category-based news for general queries
                    category = 'general'
                    if any(keyword in query.lower() for keyword in ['business', 'technology', 'sports', 'health', 'science', 'entertainment']):
                        for cat in ['business', 'technology', 'sports', 'health', 'science', 'entertainment']:
                            if cat in query.lower():
                                category = cat
                                break
                    return {"category": category}
            else:
                return {"query": query}
        
        # Default fallback
        return {"query": query}

    def _parse_mongodb_query(self, query: str) -> tuple:
        """Parse user query to extract MongoDB collection name and query"""
        query_lower = query.lower()

        # Common collection names to look for
        common_collections = ['users', 'products', 'orders', 'customers', 'items', 'data', 'records', 'documents', 'posts', 'comments']

        # Try to extract collection name from query
        collection_name = "data"  # default collection
        mongo_query = "{}"  # default query (all documents)

        # Look for explicit collection mentions
        for collection in common_collections:
            if collection in query_lower:
                collection_name = collection
                break

        # Look for specific query patterns
        if "find" in query_lower or "search" in query_lower:
            # Try to extract search criteria
            if "name" in query_lower:
                # Extract name search
                import re
                name_match = re.search(r'name.*?["\']([^"\']+)["\']', query_lower)
                if name_match:
                    search_name = name_match.group(1)
                    mongo_query = f'{{"name": {{"$regex": "{search_name}", "$options": "i"}}}}'
                elif "=" in query:
                    # Handle name=value format
                    parts = query.split("=")
                    if len(parts) == 2:
                        field = parts[0].strip().split()[-1]  # Get last word before =
                        value = parts[1].strip().strip('"\'')
                        mongo_query = f'{{"{field}": "{value}"}}'
            elif "id" in query_lower:
                # Extract ID search
                import re
                id_match = re.search(r'id.*?["\']?([a-zA-Z0-9]+)["\']?', query_lower)
                if id_match:
                    search_id = id_match.group(1)
                    mongo_query = f'{{"_id": "{search_id}"}}'

        # If query contains JSON-like structure, try to use it
        if "{" in query and "}" in query:
            try:
                import json
                # Extract JSON part
                start = query.find("{")
                end = query.rfind("}") + 1
                json_part = query[start:end]
                # Validate JSON
                json.loads(json_part)
                mongo_query = json_part
            except:
                pass  # Keep default query

        return collection_name, mongo_query

    def _extract_source_urls(self, content: str) -> List[str]:
        """Extract URLs from the content"""
        import re

        # Pattern to match URLs in the content
        url_patterns = [
            r'URL: (https?://[^\s\n]+)',  # URL: https://...
            r'Link: (https?://[^\s\n]+)',  # Link: https://...
            r'Source: (https?://[^\s\n]+)',  # Source: https://...
            r'\((https?://[^\s\)]+)\)',  # (https://...)
            r'https?://[^\s\n\)]+',  # Direct URLs
        ]

        urls = []
        for pattern in url_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if isinstance(matches[0] if matches else None, tuple):
                # If pattern has groups, extract the first group
                urls.extend([match[0] if isinstance(match, tuple) else match for match in matches])
            else:
                urls.extend(matches)

        # Remove duplicates and limit to 5 URLs
        unique_urls = []
        for url in urls:
            if url not in unique_urls and len(unique_urls) < 5:
                unique_urls.append(url)

        return unique_urls

    def _extract_location_from_weather_query(self, query: str) -> str:
        """Extract location from weather-related queries"""
        import re

        # Remove common weather-related words
        weather_words = ['weather', 'temperature', 'forecast', 'climate', 'today', 'current', 'in', 'for', 'of', 'the']

        # Clean the query
        cleaned_query = query.lower()

        # Remove weather-related words
        for word in weather_words:
            cleaned_query = re.sub(r'\b' + word + r'\b', '', cleaned_query)

        # Remove extra spaces and clean up
        cleaned_query = re.sub(r'\s+', ' ', cleaned_query).strip()

        # If nothing left, try to extract from original query using patterns
        if not cleaned_query:
            # Look for patterns like "weather in New York", "New York weather", etc.
            patterns = [
                r'weather\s+in\s+([^,]+)',  # "weather in New York"
                r'([^,]+)\s+weather',       # "New York weather"
                r'weather\s+for\s+([^,]+)', # "weather for New York"
                r'weather\s+of\s+([^,]+)',  # "weather of New York"
            ]

            for pattern in patterns:
                match = re.search(pattern, query.lower())
                if match:
                    location = match.group(1).strip()
                    # Capitalize properly
                    return ' '.join(word.capitalize() for word in location.split())

            # If no pattern matches, return the original query
            return query

        # Capitalize the cleaned location properly
        return ' '.join(word.capitalize() for word in cleaned_query.split())

    async def cleanup(self):
        """Clean up resources"""
        try:
            # No persistent connections to clean up since we use context managers
            self.status = "disconnected"
            logger.info(f"Cleaned up agent {self.agent_id}")
        except Exception as e:
            logger.error(f"Error cleaning up agent {self.agent_id}: {e}")

    def get_info(self) -> Dict[str, Any]:
        """Get agent information"""
        return {
            "agent_id": self.agent_id,
            "user_id": self.user_id,
            "server_id": self.server_id,
            "agent_name": self.agent_name,
            "server_url": None,  # No URL with stdio transport
            "status": self.status,
            "created_at": self.created_at,
            "last_used": self.last_used
        }

class DynamicAgentManager:
    """Manager for all dynamic agents"""
    
    def __init__(self):
        self.agents: Dict[str, DynamicAgent] = {}
        self.user_agents: Dict[str, List[str]] = {}  # user_id -> list of agent_ids
        self._factory = None  # Allow injection of factory instance
        logger.info("Dynamic Agent Manager initialized")
    
    async def create_agent(self, user_id: str, server_id: str, agent_name: str, script_path: str) -> str:
        """Create a new dynamic agent for a user's MCP server"""
        try:
            # Validate server_id using the injected factory or global factory instance
            if self._factory:
                factory = self._factory
            else:
                from server.dynamic_mcp_factory import dynamic_mcp_factory
                factory = dynamic_mcp_factory

            server_info = factory.get_server_info(server_id)
            if not server_info:
                available_servers = factory.list_servers()
                logger.error(f"Server {server_id} not found. Available servers: {[s['server_id'] for s in available_servers]}")
                raise Exception(f"Server {server_id} not found")

            server_type = server_info.get("server_type")
            category = server_info.get("category")
            service = server_info.get("service")

            # Validate server type and category
            valid_categories = {
                "custom": ["cloud_storage", "databases", "devops", "git"],
                "public": ["locally_available"]
            }
            valid_services = {
                "cloud_storage": ["aws_s3", "gcs_drive", "google_drive", "microsoft_sharepoint", "azure_blob", "onedrive"],
                "databases": ["postgres", "mysql", "mongodb", "redis", "sql_server"],
                "devops": ["jira", "azure_devops"],
                "git": ["github", "bitbucket", "azure_repos"],
                "locally_available": ["airbnb", "news", "google", "weather", "brave_search", "google_maps"]
            }

            # Validate server type and category
            if server_type not in valid_categories:
                raise Exception(f"Invalid server type {server_type}")

            if not category or category not in valid_categories[server_type]:
                raise Exception(f"Invalid category {category} for server type {server_type}")

            if not service or service not in valid_services[category]:
                raise Exception(f"Invalid service {service} for category {category} in server type {server_type}")

            agent_id = f"agent_{uuid.uuid4().hex[:8]}"

            # Create agent with script_path
            agent = DynamicAgent(agent_id, user_id, server_id, agent_name, script_path)

            # Pass the factory instance to the agent if available
            if self._factory:
                agent._factory = self._factory

            # Initialize agent
            if not await agent.initialize():
                raise Exception("Failed to initialize agent")

            # Store agent
            self.agents[agent_id] = agent

            # Track user's agents
            if user_id not in self.user_agents:
                self.user_agents[user_id] = []
            self.user_agents[user_id].append(agent_id)

            logger.info(f"Created dynamic agent {agent_id} for user {user_id} with server type {server_type} and service {service}")
            return agent_id

        except Exception as e:
            logger.error(f"Error creating agent for user {user_id}: {e}")
            raise
    
     
    def _analyze_query_intent(self, query: str) -> Dict[str, Any]:
        """Analyze query to determine which type of MCP server would be best"""
        query_lower = query.lower()

        # Define keywords for different service types
        service_keywords = {
            # Public/Locally Available Services
            "weather": [
                "weather", "temperature", "rain", "snow", "sunny", "cloudy", "forecast",
                "climate", "humidity", "wind", "storm", "hot", "cold", "degrees", "celsius", "fahrenheit"
            ],
            "news": [
                "news", "latest", "breaking", "headlines", "article", "report", "journalist",
                "politics", "election", "government", "current events", "today's news", "recent",
                "war", "conflict", "crisis", "international", "world news", "breaking news",
                "press", "media", "story", "coverage", "update", "bulletin", "alert"
            ],
            "airbnb": [
                "hotel", "accommodation", "stay", "booking", "room", "lodge", "resort",
                "guest house", "bed and breakfast", "vacation rental", "place to stay", "hotels"
            ],
            "google": [
                "search", "find", "google", "web search", "look up", "information about",
                "what is", "who is", "how to", "definition", "explain", "names", "search engine", "information",
                "person names", "specific names"
            ],
            "brave_search": [
                "search", "find", "look up", "information", "research", "browse",
                "web search", "internet search", "query", "investigate"
            ],
            "google_maps": [
                "directions", "route", "map", "location", "address", "navigate", "distance",
                "places near", "restaurants near", "shops near", "how to get to", "where is",
                "geocode", "coordinates", "latitude", "longitude"
            ],

            # Cloud Storage Services
            "aws_s3": [
                "s3", "aws", "amazon", "bucket", "object", "file", "upload", "download", "storage",
                "cloud storage", "s3 bucket", "aws storage", "amazon s3", "blob", "object storage"
            ],
            "gcs_drive": [
                "gcs", "google cloud", "cloud storage", "bucket", "blob", "file", "upload", "download",
                "google cloud storage", "gcp storage", "cloud bucket", "google storage"
            ],
            "google_drive": [
                "files", "documents", "drive", "folder", "upload", "download", "share",
                "google drive", "my files", "document", "spreadsheet", "presentation"
            ],
            "azure_blob": [
                "azure", "blob", "microsoft", "azure storage", "container", "file", "upload", "download",
                "azure blob storage", "microsoft azure", "cloud storage", "azure container"
            ],
            "onedrive": [
                "onedrive", "microsoft", "office", "sharepoint", "file", "document", "folder",
                "one drive", "microsoft drive", "office 365", "sync", "share"
            ],
            "microsoft_sharepoint": [
                "sharepoint", "microsoft", "office", "collaboration", "document", "site", "list",
                "office 365", "teams", "intranet", "portal", "workspace"
            ],

            # Database Services
            "postgres": [
                "postgres", "postgresql", "database", "sql", "query", "table", "select", "insert",
                "update", "delete", "db", "relational", "psql", "pg"
            ],
            "mysql": [
                "mysql", "database", "sql", "query", "table", "select", "insert", "update", "delete",
                "db", "relational", "mariadb", "my sql"
            ],
            "mongodb": [
                "mongodb", "mongo", "database", "nosql", "collection", "document", "find", "insert",
                "update", "delete", "db", "non-relational", "bson", "atlas"
            ],
            "redis": [
                "redis", "cache", "key-value", "memory", "session", "store", "get", "set",
                "hash", "list", "sorted set", "pub/sub", "in-memory"
            ],
            "sql_server": [
                "sql server", "mssql", "microsoft sql", "database", "sql", "query", "table",
                "select", "insert", "update", "delete", "db", "t-sql", "ssms"
            ],

            # DevOps Services
            "jira": [
                "jira", "ticket", "issue", "bug", "task", "story", "epic", "sprint", "project",
                "atlassian", "workflow", "kanban", "scrum", "agile"
            ],
            "azure_devops": [
                "azure devops", "devops", "pipeline", "build", "release", "repository", "work item",
                "board", "microsoft devops", "vsts", "tfs", "ci/cd"
            ],

            # Git Services
            "github": [
                "github", "git", "repository", "repo", "commit", "branch", "pull request", "pr",
                "issue", "code", "version control", "merge", "fork", "clone"
            ],
            "bitbucket": [
                "bitbucket", "git", "repository", "repo", "commit", "branch", "pull request", "pr",
                "atlassian", "code", "version control", "merge", "fork", "clone"
            ],
            "azure_repos": [
                "azure repos", "git", "repository", "repo", "commit", "branch", "pull request", "pr",
                "microsoft", "code", "version control", "merge", "fork", "clone", "devops"
            ]
        }

         # Score each service based on keyword matches
        service_scores = {}
        query_words = query_lower.split()  # Split query into individual words

        for service, keywords in service_keywords.items():
            score = 0
            for keyword in keywords:
                keyword_words = keyword.split()

                if len(keyword_words) == 1:
                    # Single word keyword - check for exact word match
                    if keyword in query_words:
                        score += 3  # Higher score for exact word matches
                    elif keyword in query_lower:
                        score += 1  # Lower score for substring matches
                else:
                    # Multi-word keyword - check for phrase match
                    if keyword in query_lower:
                        score += len(keyword_words) * 3  # Higher score for phrase matches

            service_scores[service] = score

        # Find the best matching service
        best_service = max(service_scores, key=service_scores.get) if service_scores else None
        best_score = service_scores.get(best_service, 0) if best_service else 0

        # Debug logging for troubleshooting
        top_services = sorted(service_scores.items(), key=lambda x: x[1], reverse=True)[:5]
        logger.debug(f"Query: '{query}' -> Top services: {dict(top_services)}")

        return {
            "best_service": best_service,
            "confidence": best_score,
            "all_scores": service_scores,
            "query_analysis": {
                "has_location_intent": any(word in query_lower for word in ["near", "in", "at", "location", "address", "where"]),
                "has_time_intent": any(word in query_lower for word in ["today", "tomorrow", "now", "current", "latest", "recent"]),
                "has_search_intent": any(word in query_lower for word in ["search", "find", "look", "what", "how", "who", "when"])
            }
        }

    def _select_best_agent(self, user_id: str, query: str, mode: str = "dynamic_mcp") -> Optional[str]:
        """Select the best agent for a given query based on intelligent analysis"""
        user_agent_ids = self.user_agents.get(user_id, [])

        if not user_agent_ids:
            return None
         # Only use intelligent selection for dynamic_mcp mode
        if mode != "dynamic_mcp":
            logger.info(f"Mode '{mode}' detected - using most recent agent (no intelligent selection)")
            return user_agent_ids[-1]

        # Analyze the query to understand intent (only for dynamic_mcp mode)
        intent_analysis = self._analyze_query_intent(query)
        best_service = intent_analysis["best_service"]
        confidence = intent_analysis["confidence"]

        logger.info(f"Dynamic MCP mode - Query intent analysis: {intent_analysis}")

        # If confidence is too low, use the most recent agent as fallback
        if confidence < 2:
            logger.info(f"Low confidence ({confidence}) for query intent, using most recent agent")
            return user_agent_ids[-1]

        # Try to find an agent that matches the best service
        for agent_id in user_agent_ids:
            agent = self.agents.get(agent_id)
            if agent:
                # Get server info to determine service type
                try:
                    if self._factory:
                        factory = self._factory
                    else:
                        from server.dynamic_mcp_factory import dynamic_mcp_factory
                        factory = dynamic_mcp_factory

                    server_info = factory.get_server_info(agent.server_id)
                    if server_info and server_info.get("service") == best_service:
                        logger.info(f"Found matching agent {agent_id} for service {best_service}")
                        return agent_id
                except Exception as e:
                    logger.warning(f"Could not get server info for agent {agent_id}: {e}")
                    continue

        # If no exact match found, try partial matches or related services
        related_services = {
            # Public Services
            "weather": ["weather"],
            "news": ["news"],
            "airbnb": ["airbnb"],
            "google": ["google", "brave_search"],  # Both are search engines
            "brave_search": ["brave_search", "google"],  # Both are search engines
            "google_maps": ["google_maps"],

            # Cloud Storage Services (can be related for file operations)
            "aws_s3": ["aws_s3", "gcs_drive", "azure_blob"],  # All cloud storage
            "gcs_drive": ["gcs_drive", "aws_s3", "azure_blob"],  # All cloud storage
            "google_drive": ["google_drive", "onedrive", "microsoft_sharepoint"],  # Document storage
            "azure_blob": ["azure_blob", "aws_s3", "gcs_drive"],  # All cloud storage
            "onedrive": ["onedrive", "google_drive", "microsoft_sharepoint"],  # Microsoft ecosystem
            "microsoft_sharepoint": ["microsoft_sharepoint", "onedrive", "google_drive"],  # Document collaboration

            # Database Services (can be related for data queries)
            "postgres": ["postgres", "mysql", "sql_server"],  # All SQL databases
            "mysql": ["mysql", "postgres", "sql_server"],  # All SQL databases
            "mongodb": ["mongodb"],  # NoSQL is different
            "redis": ["redis"],  # Cache is different
            "sql_server": ["sql_server", "postgres", "mysql"],  # All SQL databases

            # DevOps Services
            "jira": ["jira", "azure_devops"],  # Project management tools
            "azure_devops": ["azure_devops", "jira"],  # Project management tools

            # Git Services (all are version control)
            "github": ["github", "bitbucket", "azure_repos"],  # All git services
            "bitbucket": ["bitbucket", "github", "azure_repos"],  # All git services
            "azure_repos": ["azure_repos", "github", "bitbucket"]  # All git services
        }

        if best_service in related_services:
            for agent_id in user_agent_ids:
                agent = self.agents.get(agent_id)
                if agent:
                    try:
                        if self._factory:
                            factory = self._factory
                        else:
                            from server.dynamic_mcp_factory import dynamic_mcp_factory
                            factory = dynamic_mcp_factory

                        server_info = factory.get_server_info(agent.server_id)
                        if server_info and server_info.get("service") in related_services[best_service]:
                            logger.info(f"Found related agent {agent_id} for service {best_service}")
                            return agent_id
                    except Exception as e:
                        continue

        # Fallback to most recent agent
        logger.info(f"No matching agent found for service {best_service}, using most recent agent")
        return user_agent_ids[-1]

    async def query_user_agent(self, user_id: str, query: str, mode: str = "dynamic_mcp") -> Dict[str, Any]:
        """Query a user's specialized agent with intelligent server selection (only for dynamic_mcp mode)"""
        try:
            # Get user's agents
            user_agent_ids = self.user_agents.get(user_id, [])

            if not user_agent_ids:
                 # Only provide intelligent fallback for dynamic_mcp mode
                if mode == "dynamic_mcp":
                    intent_analysis = self._analyze_query_intent(query)
                    best_service = intent_analysis.get("best_service", "unknown")
                    confidence = intent_analysis.get("confidence", 0)
                else:
                    # For other modes, use simple fallback
                    return {
                        "success": False,
                        "error": "No specialized agents found for user",
                        "result": "No dynamic MCP servers available for this user.",
                        "agent_type": "dynamic"
                    }

                # Enhanced fallback message for dynamic_mcp mode
                intent_analysis = self._analyze_query_intent(query)
                best_service = intent_analysis.get("best_service", "unknown")
                confidence = intent_analysis.get("confidence", 0)

                # Create more specific fallback messages based on service type
                service_categories = {
                    # Public services
                    "weather": ("public", "locally_available"),
                    "news": ("public", "locally_available"),
                    "airbnb": ("public", "locally_available"),
                    "google": ("public", "locally_available"),
                    "brave_search": ("public", "locally_available"),
                    "google_maps": ("public", "locally_available"),

                    # Cloud storage
                    "aws_s3": ("custom", "cloud_storage"),
                    "gcs_drive": ("custom", "cloud_storage"),
                    "google_drive": ("custom", "cloud_storage"),
                    "azure_blob": ("custom", "cloud_storage"),
                    "onedrive": ("custom", "cloud_storage"),
                    "microsoft_sharepoint": ("custom", "cloud_storage"),

                    # Databases
                    "postgres": ("custom", "databases"),
                    "mysql": ("custom", "databases"),
                    "mongodb": ("custom", "databases"),
                    "redis": ("custom", "databases"),
                    "sql_server": ("custom", "databases"),

                    # DevOps
                    "jira": ("custom", "devops"),
                    "azure_devops": ("custom", "devops"),

                    # Git
                    "github": ("custom", "git"),
                    "bitbucket": ("custom", "git"),
                    "azure_repos": ("custom", "git")
                }

                server_type, category = service_categories.get(best_service, ("unknown", "unknown"))

                if confidence >= 2:  # High confidence in service detection
                    fallback_message = f"Sorry, I cannot process your query about {best_service}. "
                    fallback_message += f"To proceed with your query, you need to create a dynamic MCP server for {best_service}. "

                    if server_type != "unknown":
                        fallback_message += f"\\n\\nTo create a {best_service} server:"
                        fallback_message += f"\\n- Server Type: {server_type}"
                        fallback_message += f"\\n- Category: {category}"
                        fallback_message += f"\\n- Service: {best_service}"

                        # Add specific credential hints
                        if best_service in ["aws_s3"]:
                            fallback_message += "\\n- Required: AWS access key, secret key, bucket name, region"
                        elif best_service in ["google_drive"]:
                            fallback_message += "\\n- Required: Google OAuth client ID, client secret, refresh token"
                        elif best_service in ["postgres", "mysql"]:
                            fallback_message += "\\n- Required: Database host, port, username, password, database name"
                        elif best_service in ["github"]:
                            fallback_message += "\\n- Required: GitHub personal access token, repository owner, repository name"
                        elif best_service in ["jira"]:
                            fallback_message += "\\n- Required: Jira URL, username, API token, project key"
                        elif best_service in ["weather", "news"]:
                            fallback_message += "\\n- Required: API key from the service provider"

                    fallback_message += "\\n\\nPlease create the appropriate MCP server first and then ask your question again."
                else:
                    # Low confidence - generic message
                    fallback_message = "Sorry, I cannot determine which type of MCP server would be best for your query. "
                    fallback_message += "Please create a dynamic MCP server that matches your needs and then ask your question again."

                return {
                    "success": False,
                    "error": "No specialized agents found for user",
                    "result": fallback_message,
                    "agent_type": "dynamic",
                    "suggested_service": best_service,
                    "suggested_server_type": server_type,
                    "suggested_category": category,
                    "intent_analysis": intent_analysis
                }

            # Select the best agent for this query (with mode-aware selection)
            selected_agent_id = self._select_best_agent(user_id, query, mode)
            agent = self.agents.get(selected_agent_id)

            if not agent:
                return {
                    "success": False,
                    "error": "Selected agent not found",
                    "result": "",
                    "agent_type": "dynamic"
                }

            # Log which agent was selected
            try:
                if self._factory:
                    factory = self._factory
                else:
                    from server.dynamic_mcp_factory import dynamic_mcp_factory
                    factory = dynamic_mcp_factory

                server_info = factory.get_server_info(agent.server_id)
                service_type = server_info.get("service", "unknown") if server_info else "unknown"
                logger.info(f"Selected agent {selected_agent_id} (service: {service_type}) for query: {query[:50]}...")
            except Exception as e:
                logger.warning(f"Could not log agent selection details: {e}")

            # Query the selected agent
            result = await agent.query(query)

            # Add selection info to result
            if isinstance(result, dict):
                result["selected_agent_id"] = selected_agent_id
                result["agent_selection_method"] = "intelligent"

            return result

        except Exception as e:
            logger.error(f"Error querying user agent for {user_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": "",
                "agent_type": "dynamic"
            }
    
    def get_user_agents(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all agents for a user with enhanced service information"""
        user_agent_ids = self.user_agents.get(user_id, [])
        agents_info = []

        for agent_id in user_agent_ids:
            if agent_id in self.agents:
                agent_info = self.agents[agent_id].get_info()

                # Add service type information
                try:
                    if self._factory:
                        factory = self._factory
                    else:
                        from server.dynamic_mcp_factory import dynamic_mcp_factory
                        factory = dynamic_mcp_factory

                    server_info = factory.get_server_info(self.agents[agent_id].server_id)
                    if server_info:
                        agent_info["service_type"] = server_info.get("service", "unknown")
                        agent_info["category"] = server_info.get("category", "unknown")
                    else:
                        agent_info["service_type"] = "unknown"
                        agent_info["category"] = "unknown"
                except Exception as e:
                    logger.warning(f"Could not get service info for agent {agent_id}: {e}")
                    agent_info["service_type"] = "unknown"
                    agent_info["category"] = "unknown"

                agents_info.append(agent_info)

        return agents_info

    def get_user_agents_summary(self, user_id: str) -> Dict[str, Any]:
        """Get a summary of user's agents organized by service type"""
        agents = self.get_user_agents(user_id)

        summary = {
            "total_agents": len(agents),
            "services": {},
            "agent_details": agents
        }

        for agent in agents:
            service_type = agent.get("service_type", "unknown")
            if service_type not in summary["services"]:
                summary["services"][service_type] = []
            summary["services"][service_type].append({
                "agent_id": agent["agent_id"],
                "agent_name": agent["agent_name"],
                "status": agent["status"],
                "created_at": agent["created_at"]
            })

        return summary
    
    def get_all_agents(self) -> List[Dict[str, Any]]:
        """Get all agents"""
        return [agent.get_info() for agent in self.agents.values()]
    
    async def delete_agent(self, agent_id: str) -> bool:
        """Delete a dynamic agent"""
        try:
            if agent_id not in self.agents:
                return False

            agent = self.agents[agent_id]
            user_id = agent.user_id

            # Clean up agent resources
            await agent.cleanup()

            # Remove from user's agents
            if user_id in self.user_agents:
                self.user_agents[user_id] = [aid for aid in self.user_agents[user_id] if aid != agent_id]
                if not self.user_agents[user_id]:
                    del self.user_agents[user_id]

            # Remove agent
            del self.agents[agent_id]

            logger.info(f"Deleted dynamic agent {agent_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting agent {agent_id}: {e}")
            return False

# Global agent manager instance (singleton pattern)
_global_agent_manager = None

def get_global_agent_manager():
    """Get the global agent manager instance (singleton)"""
    global _global_agent_manager
    if _global_agent_manager is None:
        _global_agent_manager = DynamicAgentManager()
    return _global_agent_manager

# Functions for external use
async def create_dynamic_agent(user_id: str, server_id: str, agent_name: str, script_path: str) -> str:
    """Create a dynamic agent using the global manager"""
    manager = get_global_agent_manager()
    return await manager.create_agent(user_id, server_id, agent_name, script_path)

#async def query_user_dynamic_agent(user_id: str, query: str) -> Dict[str, Any]:
    #"""Query a user's dynamic agent"""
    #return await dynamic_agent_manager.query_user_agent(user_id, query)

async def query_user_dynamic_agent(user_id: str, query: str, mode: str = "dynamic_mcp") -> Dict[str, Any]:
    """Query a user's dynamic agent with mode-aware intelligent selection"""
    manager = get_global_agent_manager()
    result = await manager.query_user_agent(user_id, query, mode)
    return result

async def query_user_dynamic_agent_string(user_id: str, query: str, mode: str = "dynamic_mcp") -> str:
    """Query a user's dynamic agent and return string result (for backward compatibility)"""
    manager = get_global_agent_manager()
    result = await manager.query_user_agent(user_id, query, mode)
    if result.get("success", False):
        return result.get("result", "")
    else:
        return f"Error: {result.get('error', 'Unknown error')}"

def get_user_dynamic_agents(user_id: str) -> List[Dict[str, Any]]:
    """Get all dynamic agents for a user"""
    manager = get_global_agent_manager()
    return manager.get_user_agents(user_id)

def get_all_dynamic_agents() -> List[Dict[str, Any]]:
    """Get all dynamic agents"""
    manager = get_global_agent_manager()
    return manager.get_all_agents()

async def delete_dynamic_agent(agent_id: str) -> bool:
    """Delete a dynamic agent"""
    manager = get_global_agent_manager()
    return await manager.delete_agent(agent_id)