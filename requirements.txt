# Core FastAPI and Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
aiohttp>=3.8.0

# Environment and Configuration
python-dotenv>=1.0.0
pydantic==2.8.2
typing-extensions==4.13.2

# Database and Storage
pymongo>=4.5.0
redis[hiredis]==5.0.8
qdrant-client>=1.6.0

# LangChain and AI Framework
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-groq>=0.1.0
langchain-huggingface>=0.0.3
langchain_ollama>=0.1.0
langgraph>=0.1.0
langchain-mcp-adapters>=0.1.0

# MCP (Model Context Protocol)
mcp>=1.0.0
mcp[cli]>=1.0.0
fastmcp>=0.1.0

# Machine Learning and NLP
sentence-transformers>=2.2.0
transformers>=4.30.0
torch>=2.0.0
numpy>=1.24.0
pandas>=2.0.0

# AI-powered query analysis
openai>=1.0.0
scikit-learn>=1.3.0

# Security and Guardrails
guardrails-ai>=0.4.0
presidio-analyzer>=2.2.0
cryptography>=3.4.8

# HTTP and API Clients
requests>=2.31.0
httpx>=0.25.0
packaging>=23.0

# Document Processing
PyPDF2>=3.0.0
python-docx>=0.8.11
openpyxl>=3.1.0
pillow>=10.0.0
pdf2image>=1.16.0
python-magic>=0.4.27

# OCR (Optional - for document processing)
pytesseract>=0.3.10

# Cloud Storage Dependencies
boto3>=1.26.0
google-cloud-storage>=2.7.0
azure-storage-blob>=12.14.0

# Google APIs
google-api-python-client>=2.0.0
google-auth-httplib2>=0.1.0
google-auth-oauthlib>=0.5.0

# Database Drivers
psycopg2-binary>=2.9.0
mysql-connector-python>=8.0.0
pyodbc>=4.0.0

# Microsoft Graph API (for OneDrive/SharePoint)
msal>=1.24.0
msgraph-core>=0.2.2

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# Additional Utilities
uuid
datetime
pathlib
shutil
tempfile
subprocess
asyncio
json
logging
os
sys
re
ssl
enum
contextlib