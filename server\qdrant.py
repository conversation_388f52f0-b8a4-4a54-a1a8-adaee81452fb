#!/usr/bin/env python3
"""
Qdrant MCP Server with stdio transport
This file is started automatically by qdrant_agent.py
"""
import logging
from typing import Any
import httpx
from mcp.server.fastmcp import FastMCP
import os
import sys
from sentence_transformers import SentenceTransformer
import asyncio
from dotenv import load_dotenv
# Pydantic imports removed - not needed for this implementation

# Load environment variables
load_dotenv()

# Configure logging to stderr to avoid interfering with stdio
logging.basicConfig(
    level=logging.ERROR,
    format='%(asctime)s %(levelname)s %(message)s',
    stream=sys.stderr
)

# Initialize MCP server
mcp = FastMCP("qdrant")

# Environment variables for config
QDRANT_URL = os.environ.get("QDRANT_URL")
QDRANT_API_KEY = os.environ.get("QDRANT_API_KEY")
COLLECTION_NAME = os.environ.get("COLLECTION_NAME")
EMBEDDING_MODEL = os.environ.get("EMBEDDING_MODEL", "sentence-transformers/all-MiniLM-L6-v2")

# Check required environment variables
if not QDRANT_URL:
    print("ERROR: QDRANT_URL not found in environment!", file=sys.stderr)
    sys.exit(1)

if not QDRANT_API_KEY:
    print("ERROR: QDRANT_API_KEY not found in environment!", file=sys.stderr)
    sys.exit(1)

# Initialize model variable (will be loaded on first use)
model = None

def get_model():
    """Load the embedding model on first use"""
    global model
    if model is None:
        try:
            model = SentenceTransformer(EMBEDDING_MODEL)
        except Exception as e:
            print(f"ERROR: Failed to load embedding model {EMBEDDING_MODEL}: {e}", file=sys.stderr)
            raise e
    return model

# Set headers
HEADERS = {
    "Authorization": f"Bearer {QDRANT_API_KEY}",
    "Content-Type": "application/json"
}

# Tool arguments are handled directly by FastMCP

# Search Qdrant vector DB
async def search_qdrant_context(query: str, collection_name: str = COLLECTION_NAME, top_k: int = 5) -> list:
    try:
        embedding_model = get_model()
        embedding = embedding_model.encode(query).tolist()
        payload = {
            "vector": embedding,
            "limit": top_k,
            "with_payload": True
        }
        url = f"{QDRANT_URL}/collections/{collection_name}/points/search"
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=HEADERS, json=payload)
            response.raise_for_status()
            results = response.json().get("result", [])
            if not results:
                return []
            formatted_results = [
                {
                    "text": item["payload"].get("content", ""),
                    "score": item.get("score", 0.0),
                    "metadata": {
                        "source": item["payload"].get("filename", "unknown_file"),
                        "collection": collection_name
                    }
                } for item in results
            ]
            return formatted_results
    except Exception as e:
        print(f"Error querying Qdrant: {str(e)}", file=sys.stderr)
        return []

@mcp.tool()
async def qdrant_find(query: str, collection_name: str = COLLECTION_NAME) -> list:
    """
    Search Qdrant vector database for relevant content.

    Args:
        query: The search query string
        collection_name: Name of the Qdrant collection to search (default from env)

    Returns:
        List of search results with text, score, and metadata
    """
    return await search_qdrant_context(query, collection_name=collection_name, top_k=10)

if __name__ == "__main__":
    # Start server with stdio transport
    mcp.run(transport="stdio")
